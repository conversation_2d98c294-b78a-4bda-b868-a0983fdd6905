Metadata-Version: 2.1
Name: Flask-WTF
Version: 1.2.1
Summary: Form rendering, validation, and CSRF protection for Flask with WTForms.
Project-URL: Documentation, https://flask-wtf.readthedocs.io/
Project-URL: Changes, https://flask-wtf.readthedocs.io/changes/
Project-URL: Source Code, https://github.com/wtforms/flask-wtf/
Project-URL: Issue Tracker, https://github.com/wtforms/flask-wtf/issues/
Project-URL: Chat, https://discord.gg/pallets
Maintainer: WTForms
License: Copyright 2010 WTForms
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are
        met:
        
        1.  Redistributions of source code must retain the above copyright
            notice, this list of conditions and the following disclaimer.
        
        2.  Redistributions in binary form must reproduce the above copyright
            notice, this list of conditions and the following disclaimer in the
            documentation and/or other materials provided with the distribution.
        
        3.  Neither the name of the copyright holder nor the names of its
            contributors may be used to endorse or promote products derived from
            this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
        "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
        LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
        HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
        SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
        TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
        PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
        LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
        NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
        SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE.rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Internet :: WWW/HTTP :: WSGI
Classifier: Topic :: Internet :: WWW/HTTP :: WSGI :: Application
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Requires-Python: >=3.8
Requires-Dist: flask
Requires-Dist: itsdangerous
Requires-Dist: wtforms
Provides-Extra: email
Requires-Dist: email-validator; extra == 'email'
Description-Content-Type: text/x-rst

Flask-WTF
=========

Simple integration of Flask and WTForms, including CSRF, file upload,
and reCAPTCHA.

Links
-----

-   Documentation: https://flask-wtf.readthedocs.io/
-   Changes: https://flask-wtf.readthedocs.io/changes/
-   PyPI Releases: https://pypi.org/project/Flask-WTF/
-   Source Code: https://github.com/wtforms/flask-wtf/
-   Issue Tracker: https://github.com/wtforms/flask-wtf/issues/
-   Chat: https://discord.gg/pallets
